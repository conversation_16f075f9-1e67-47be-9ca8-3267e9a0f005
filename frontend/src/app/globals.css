@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:where(.dark, .dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --animate-accordion-down: accordion-down 0.2s ease-out;
  --animate-accordion-up: accordion-up 0.2s ease-out;
  --animate-marquee: marquee var(--duration) infinite linear;
  --animate-marquee-vertical: marquee-vertical var(--duration) linear infinite;
  --animate-orbit: orbit calc(var(--duration) * 1s) linear infinite;
  --animate-slide-down: slide-down 300ms cubic-bezier(0.87, 0, 0.13, 1);
  --animate-slide-up: slide-up 300ms cubic-bezier(0.87, 0, 0.13, 1);
  --scaleIn: scaleIn 200ms ease;
  --scaleOut: scaleOut 200ms ease;
  --fadeIn: fadeIn 200ms ease;
  --fadeOut: fadeOut 200ms ease;
  --enterFromLeft: enterFromLeft 250ms ease;
  --enterFromRight: enterFromRight 250ms ease;
  --exitToLeft: exitToLeft 250ms ease;
  --exitToRight: exitToRight 250ms ease;
  --animate-elliptical-orbit: elliptical-orbit 20s linear infinite;
  @keyframes orbit {
    0% {
      transform: rotate(calc(var(--angle) * 1deg))
        translateY(calc(var(--radius) * 1px)) rotate(calc(var(--angle) * -1deg));
    }
    100% {
      transform: rotate(calc(var(--angle) * 1deg + 360deg))
        translateY(calc(var(--radius) * 1px))
        rotate(calc((var(--angle) * -1deg) - 360deg));
    }
  }

  @keyframes accordion-down {
    from {
      height: 0;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }

  @keyframes accordion-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0;
    }
  }

  @keyframes marquee {
    from {
      transform: translateX(0);
    }
    to {
      transform: translateX(calc(-100% - var(--gap)));
    }
  }

  @keyframes marquee-vertical {
    from {
      transform: translateY(0);
    }
    to {
      transform: translateY(calc(-100% - var(--gap)));
    }
  }
  @keyframes slide-down {
    from {
      height: 0px;
    }
    to {
      height: var(--radix-accordion-content-height);
    }
  }

  @keyframes slide-up {
    from {
      height: var(--radix-accordion-content-height);
    }
    to {
      height: 0px;
    }
  }

  /* Add this to your globals.css */
  @keyframes enterFromRight {
    from {
      opacity: 0;
      transform: translateX(200px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes enterFromLeft {
    from {
      opacity: 0;
      transform: translateX(-200px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes exitToRight {
    from {
      opacity: 1;
      transform: translateX(0);
    }
    to {
      opacity: 0;
      transform: translateX(200px);
    }
  }

  @keyframes exitToLeft {
    from {
      opacity: 1;
      transform: translateX(0);
    }
    to {
      opacity: 0;
      transform: translateX(-200px);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: rotateX(-30deg) scale(0.9);
    }
    to {
      opacity: 1;
      transform: rotateX(0deg) scale(1);
    }
  }

  @keyframes scaleOut {
    from {
      opacity: 1;
      transform: rotateX(0deg) scale(1);
    }
    to {
      opacity: 0;
      transform: rotateX(-10deg) scale(0.95);
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes fadeOut {
    from {
      opacity: 1;
    }
    to {
      opacity: 0;
    }
  }

  @keyframes elliptical-orbit {
    from {
      transform: rotate(var(--angle, 0) deg)
        translate(var(--h-radius, 160px), 0)
        rotate(calc(var(--angle, 0) deg * -1));
    }
    to {
      transform: rotate(calc(var(--angle, 0) deg + 360deg))
        translate(var(--h-radius, 160px), 0)
        rotate(calc((var(--angle, 0) deg + 360deg) * -1));
    }
  }
}

:root {
  --features-card-bg: #fff; /* plain white for feature cards in light mode */
  --features-card-folder-bg: #fff; /* plain white for folder cards in light mode */
  --features-bg: oklch(0.98 0 0); /* very light neutral for features section */
  --footer-link: oklch(0 0 0); /* black for light bg */
  --footer-heading: oklch(0 0 0); /* black for headings */

  --background: oklch(98.46% 0.002 247.84);
  --foreground: oklch(0 0 0); /* pure black */
  --card: oklch(1 0 0);
  --card-foreground: oklch(0 0 0); /* pure black */
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0 0 0); /* pure black */
  --primary: oklch(0 0 0); /* pure black */
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(54.65% 0.246 262.87);
  --secondary-foreground: oklch(0 0 0); /* pure black */
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0 0 0); /* pure black for muted text too */
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0 0 0); /* pure black */
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(98.46% 0.002 247.84);
  --sidebar-foreground: oklch(0 0 0); /* pure black */
  --sidebar-primary: oklch(0 0 0); /* pure black */
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0 0 0); /* pure black */
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --features-card-bg: var(
    --background
  ); /* match page background in dark mode */
  --features-card-folder-bg: var(
    --background
  ); /* match page background in dark mode */
  --features-bg: oklch(0.12 0 0); /* darker neutral for features section */
  --footer-link: oklch(0.6 0 0); /* darker gray for dark bg */
  --footer-heading: oklch(0.69 0 0); /* darker white for headings */

  --background: oklch(0.13 0.004 285.823); /* 30% darker background */
  --foreground: oklch(0.69 0 0); /* 30% darker foreground */
  --card: oklch(0.1 0 0); /* 30% darker card */
  --card-foreground: oklch(0.69 0 0); /* 30% darker card text */
  --popover: oklch(0.1 0 0); /* 30% darker popover */
  --popover-foreground: oklch(0.69 0 0); /* 30% darker popover text */
  --primary: oklch(0.69 0 0); /* 30% darker primary */
  --primary-foreground: oklch(0.14 0 0); /* 30% darker primary foreground */
  --secondary: oklch(38.26% 0.172 262.87); /* 30% darker secondary */
  --secondary-foreground: oklch(0.69 0 0); /* 30% darker secondary text */
  --muted: oklch(0.19 0 0); /* 30% darker muted */
  --muted-foreground: oklch(0.5 0 0); /* 30% darker muted text */
  --accent: oklch(19.17% 0.004 286.03); /* 30% darker accent */
  --accent-foreground: oklch(68.92% 0.001 247.84); /* 30% darker accent text */
  --destructive: oklch(0.28 0.1 25.723); /* 30% darker destructive */
  --destructive-foreground: oklch(
    0.45 0.166 25.331
  ); /* 30% darker destructive text */
  --border: oklch(0.19 0 0); /* 30% darker border */
  --input: oklch(0.19 0 0); /* 30% darker input */
  --ring: oklch(0.31 0 0); /* 30% darker ring */
  --chart-1: oklch(0.34 0.17 264.376); /* 30% darker chart 1 */
  --chart-2: oklch(0.49 0.12 162.48); /* 30% darker chart 2 */
  --chart-3: oklch(0.54 0.132 70.08); /* 30% darker chart 3 */
  --chart-4: oklch(0.44 0.186 303.9); /* 30% darker chart 4 */
  --chart-5: oklch(0.45 0.172 16.439); /* 30% darker chart 5 */
  --sidebar: oklch(14.72% 0.004 285.89); /* 30% darker sidebar */
  --sidebar-foreground: oklch(0.69 0 0); /* 30% darker sidebar text */
  --sidebar-primary: oklch(0.34 0.17 264.376); /* 30% darker sidebar primary */
  --sidebar-primary-foreground: oklch(
    0.69 0 0
  ); /* 30% darker sidebar primary text */
  --sidebar-accent: oklch(0.19 0 0); /* 30% darker sidebar accent */
  --sidebar-accent-foreground: oklch(
    0.69 0 0
  ); /* 30% darker sidebar accent text */
  --sidebar-border: oklch(0.19 0 0); /* 30% darker sidebar border */
  --sidebar-ring: oklch(0.31 0 0); /* 30% darker sidebar ring */
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  html {
    scroll-behavior: smooth;
    zoom: 100%;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: 'palt'; /* Adjusts spacing for CJK text */
  }

  /* Add font fallbacks for CJK characters */
  .cjk-text,
  .prose p,
  .prose li,
  .prose table td,
  .prose table th,
  .markdown-content {
    font-family:
      var(--font-sans),
      ui-sans-serif,
      -apple-system,
      'Segoe UI',
      'Helvetica Neue',
      'Noto Sans',
      'Noto Sans CJK JP',
      'Noto Sans CJK KR',
      'Noto Sans CJK SC',
      'Noto Sans CJK TC',
      sans-serif;
    line-height: 1.7;
  }

  /* Specific handling for monospace/code with CJK character support */
  code,
  pre,
  .font-mono {
    font-family:
      var(--font-mono), ui-monospace, SFMono-Regular, 'SF Mono', Menlo, Monaco,
      Consolas, 'Liberation Mono', 'Courier New', monospace,
      'Noto Sans Mono CJK JP', 'Noto Sans Mono CJK KR', 'Noto Sans Mono CJK SC',
      'Noto Sans Mono CJK TC';
  }
}

/* Footer link styling */
.footer-link {
  color: var(--footer-link);
  transition: color 0.2s;
}
.footer-link:hover {
  color: var(--footer-heading);
}

/* Custom styles for Markdown chat content */
.prose.chat-markdown {
  /* Ensure reasonable spacing */
  & > * + * {
    margin-top: 0.75em;
  }

  /* Fix headings to be more compact in chat context */
  & h1 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-top: 1em;
    margin-bottom: 0.5em;
    line-height: 1.2;
  }

  & h2 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-top: 0.8em;
    margin-bottom: 0.4em;
    line-height: 1.3;
  }

  & h3 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-top: 0.6em;
    margin-bottom: 0.3em;
  }

  & h4,
  & h5,
  & h6 {
    font-size: 1rem;
    font-weight: 600;
    margin-top: 0.5em;
    margin-bottom: 0.25em;
  }

  /* Improve lists */
  & ul,
  & ol {
    padding-left: 1.5em;
    margin-top: 0.5em;
    margin-bottom: 0.5em;
  }

  & ul {
    list-style-type: disc;
  }

  & ol {
    list-style-type: decimal;
  }

  & li {
    margin-top: 0.25em;
    margin-bottom: 0.25em;
  }

  & li > ul,
  & li > ol {
    margin-top: 0.25em;
    margin-bottom: 0.25em;
  }

  /* Fix code blocks */
  & pre {
    margin-top: 0.5em;
    margin-bottom: 0.5em;
    padding: 0.75em 1em;
    background-color: theme('colors.slate.100');
    border-radius: 0.375rem;
    overflow-x: auto;
    font-family: var(--font-mono);
  }

  & pre code {
    background-color: transparent;
    padding: 0;
    font-size: 0.9em;
    color: inherit;
    font-family: var(--font-mono);
    white-space: pre;
    word-break: normal;
    overflow-wrap: normal;
  }

  /* Fix inline code to ensure it wraps when needed */
  & code:not([class*='language-']) {
    padding: 0.2em 0.4em;
    font-size: 0.85em;
    font-family: var(--font-mono);
    background-color: theme('colors.slate.100');
    border-radius: 3px;
    word-break: break-word;
  }

  /* Fix tables */
  & table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 0.75em;
    margin-bottom: 0.75em;
    font-size: 0.9em;
  }

  & th {
    background-color: theme('colors.slate.100');
    font-weight: 600;
    text-align: left;
    padding: 0.5em 0.75em;
  }

  & td {
    padding: 0.5em 0.75em;
    border: 1px solid theme('colors.slate.200');
  }

  /* Fix blockquotes */
  & blockquote {
    border-left: 3px solid theme('colors.slate.300');
    padding-left: 1em;
    margin-left: 0;
    font-style: italic;
    color: theme('colors.slate.600');
  }

  /* Dark mode specific overrides - 30% darker theme */
  .dark & {
    /* Code blocks in dark mode - darker theme */
    & pre {
      background-color: oklch(0.08 0 0); /* darker code block background */
      border: 1px solid oklch(0.15 0 0); /* darker border */
      color: oklch(0.6 0 0); /* darker text */
    }

    & code:not([class*='language-']) {
      background-color: oklch(0.1 0 0); /* darker inline code background */
      color: oklch(0.55 0 0); /* darker code text */
      border: 1px solid oklch(0.15 0 0); /* darker border */
    }

    /* Tables in dark mode - darker theme */
    & th {
      background-color: oklch(0.08 0 0); /* darker table header */
      border-color: oklch(0.15 0 0); /* darker border */
      color: oklch(0.69 0 0); /* darker header text */
    }

    & td {
      border-color: oklch(0.15 0 0); /* darker table border */
      color: oklch(0.6 0 0); /* darker table text */
    }

    /* Blockquotes in dark mode - darker theme */
    & blockquote {
      border-left-color: oklch(0.25 0 0); /* darker blockquote border */
      color: oklch(0.45 0 0); /* darker blockquote text */
      background-color: oklch(0.05 0 0); /* subtle darker background */
    }

    /* Syntax highlighting in dark mode */
    & .hljs-keyword,
    & .hljs-selector-tag,
    & .hljs-built_in,
    & .hljs-name,
    & .hljs-tag {
      color: theme('colors.blue.400');
    }

    & .hljs-string,
    & .hljs-title,
    & .hljs-section,
    & .hljs-attribute,
    & .hljs-literal,
    & .hljs-template-tag,
    & .hljs-template-variable,
    & .hljs-type,
    & .hljs-addition {
      color: theme('colors.green.400');
    }

    & .hljs-comment,
    & .hljs-quote,
    & .hljs-deletion,
    & .hljs-meta {
      color: theme('colors.zinc.500');
    }

    & .hljs-keyword,
    & .hljs-selector-tag,
    & .hljs-literal,
    & .hljs-title,
    & .hljs-section,
    & .hljs-doctag,
    & .hljs-type,
    & .hljs-name,
    & .hljs-strong {
      font-weight: bold;
    }
  }
}

@layer utilities {
  .animate-background-shine {
    animation: background-shine 2s linear infinite;
  }

  @keyframes background-shine {
    from {
      background-position: 0;
    }
    to {
      background-position: -200%;
    }
  }
}
